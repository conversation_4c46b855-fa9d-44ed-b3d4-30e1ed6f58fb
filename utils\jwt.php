<?php
/**
 * JWT Utility Functions for OnlyOffice Integration
 * 
 * This file contains functions for generating and validating JWT tokens
 * for OnlyOffice Document Server authentication
 */

require_once __DIR__ . '/../config.php';

/**
 * Base64 URL-safe encoding
 */
function base64UrlEncode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

/**
 * Base64 URL-safe decoding
 */
function base64UrlDecode($data) {
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}

/**
 * Generate JWT token for OnlyOffice
 * 
 * @param array $payload The payload data to encode
 * @param string $secret The JWT secret key
 * @return string The generated JWT token
 */
function generateJWT($payload, $secret = JWT_SECRET) {
    // Header
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    
    // Add timestamp and expiration to payload
    $payload['iat'] = time();
    $payload['exp'] = time() + (60 * 60); // 1 hour expiration
    
    // Encode payload
    $payload = json_encode($payload);
    
    // Create base64 encoded header and payload
    $base64Header = base64UrlEncode($header);
    $base64Payload = base64UrlEncode($payload);
    
    // Create signature
    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
    $base64Signature = base64UrlEncode($signature);
    
    // Create JWT
    $jwt = $base64Header . "." . $base64Payload . "." . $base64Signature;
    
    logMessage("Generated JWT token for payload: " . json_encode($payload), 'DEBUG');
    
    return $jwt;
}

/**
 * Validate JWT token
 * 
 * @param string $jwt The JWT token to validate
 * @param string $secret The JWT secret key
 * @return array|false The decoded payload if valid, false otherwise
 */
function validateJWT($jwt, $secret = JWT_SECRET) {
    $parts = explode('.', $jwt);
    
    if (count($parts) !== 3) {
        logMessage("Invalid JWT format: incorrect number of parts", 'ERROR');
        return false;
    }
    
    list($base64Header, $base64Payload, $base64Signature) = $parts;
    
    // Verify signature
    $signature = base64UrlDecode($base64Signature);
    $expectedSignature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $secret, true);
    
    if (!hash_equals($signature, $expectedSignature)) {
        logMessage("JWT signature validation failed", 'ERROR');
        return false;
    }
    
    // Decode payload
    $payload = json_decode(base64UrlDecode($base64Payload), true);
    
    if (!$payload) {
        logMessage("Failed to decode JWT payload", 'ERROR');
        return false;
    }
    
    // Check expiration
    if (isset($payload['exp']) && $payload['exp'] < time()) {
        logMessage("JWT token has expired", 'ERROR');
        return false;
    }
    
    logMessage("JWT token validated successfully", 'DEBUG');
    return $payload;
}

/**
 * Generate JWT token for OnlyOffice document configuration
 * 
 * @param array $documentConfig The document configuration
 * @return string The JWT token
 */
function generateDocumentJWT($documentConfig) {
    $payload = [
        'document' => $documentConfig['document'],
        'documentType' => $documentConfig['documentType'],
        'editorConfig' => $documentConfig['editorConfig']
    ];
    
    return generateJWT($payload);
}

/**
 * Generate JWT token for callback validation
 * 
 * @param string $documentKey The document key
 * @param string $url The document URL
 * @return string The JWT token
 */
function generateCallbackJWT($documentKey, $url) {
    $payload = [
        'key' => $documentKey,
        'url' => $url,
        'type' => 'callback'
    ];
    
    return generateJWT($payload);
}

/**
 * Create authorization header for OnlyOffice requests
 * 
 * @param array $data The data to include in the token
 * @return string The authorization header value
 */
function createAuthorizationHeader($data) {
    $token = generateJWT($data);
    return "Bearer " . $token;
}

/**
 * Validate callback request from OnlyOffice
 * 
 * @param string $authHeader The authorization header from the request
 * @return array|false The validated payload or false if invalid
 */
function validateCallbackRequest($authHeader) {
    if (strpos($authHeader, 'Bearer ') !== 0) {
        logMessage("Invalid authorization header format", 'ERROR');
        return false;
    }
    
    $token = substr($authHeader, 7); // Remove "Bearer " prefix
    return validateJWT($token);
}

/**
 * Generate unique document key
 * 
 * @param string $filename The document filename
 * @param string $userId Optional user ID
 * @return string Unique document key
 */
function generateDocumentKey($filename, $userId = 'anonymous') {
    $timestamp = time();
    $random = bin2hex(random_bytes(8));
    return md5($filename . $userId . $timestamp . $random);
}

/**
 * Get document URL for OnlyOffice access
 * 
 * @param string $filename The document filename
 * @return string The document URL
 */
function getDocumentUrl($filename) {
    return APP_URL . '/documents/' . urlencode($filename);
}

/**
 * Get callback URL for document
 * 
 * @param string $documentKey The document key
 * @return string The callback URL
 */
function getCallbackUrl($documentKey) {
    return CALLBACK_URL_BASE . '/callback.php?key=' . urlencode($documentKey);
}

logMessage("JWT utilities loaded successfully", 'DEBUG');
?>
