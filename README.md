# OnlyOffice Document Server Integration - MVP

This is a Minimum Viable Product (MVP) for integrating OnlyOffice Document Server with a web application. It provides basic document creation, editing, and saving functionality.

## Features

- ✅ Create new documents (Word, Excel, PowerPoint, Text)
- ✅ Edit documents using OnlyOffice editor
- ✅ JWT authentication for secure communication
- ✅ Callback mechanism for document saving
- ✅ Document management and listing
- ✅ Docker container network compatibility

## Prerequisites

- PHP 7.4 or higher
- Apache web server with mod_rewrite enabled
- OnlyOffice Document Server running in Docker container
- Write permissions for document storage

## OnlyOffice Docker Container Setup

Your OnlyOffice container should be running with these specifications:
```bash
Container Name: onlyoffice-jwt
Container ID: c975cdcbc1c28037a57f9f8882d9d8c47611f9c102e2c364512e9f6ac2fc118a
Image: onlyoffice/documentserver:latest
Port Mappings: 8080:80, 9443:443
```

## Installation

1. **Clone/Copy the files** to your web server directory (e.g., `/xampp/htdocs/ai/ooffice/`)

2. **Configure permissions** for directories:
   ```bash
   chmod 755 documents/
   chmod 755 temp/
   chmod 755 logs/
   chmod 755 utils/
   ```

3. **Test the connection** by visiting:
   ```
   http://localhost/test-connection.php
   ```

4. **Verify OnlyOffice server** is accessible at:
   ```
   http://localhost:8080
   ```

## Configuration

The main configuration is in `config.php`. Key settings:

- **OnlyOffice Server URL**: `http://localhost:8080`
- **JWT Secret**: `mdMgBxplMBtliplZHzNRdVL6zKJLpdo2`
- **Callback URL**: `http://host.docker.internal/callback.php`

### Network Configuration for Docker

The callback URL uses `host.docker.internal` to allow the OnlyOffice container to reach back to your web application. If this doesn't work in your environment, you may need to:

1. Use your actual IP address instead of `host.docker.internal`
2. Configure Docker networking properly
3. Update the `CALLBACK_URL_BASE` in `config.php`

## Usage

1. **Access the application**:
   ```
   http://localhost/index.php
   ```

2. **Create a new document**:
   - Enter a document name
   - Select document type (Word, Excel, PowerPoint, Text)
   - Click "Create Document"

3. **Edit documents**:
   - Click "Open Editor" on any existing document
   - Use the OnlyOffice editor interface
   - Documents auto-save through callbacks

4. **Document management**:
   - View all created documents on the main page
   - See creation and modification dates
   - Open any document for editing

## File Structure

```
/
├── index.php              # Main page with document creation
├── editor.php             # OnlyOffice editor integration
├── document.php           # Document serving endpoint
├── callback.php           # OnlyOffice callback handler
├── config.php             # Configuration settings
├── test-connection.php    # Connection testing utility
├── .htaccess             # Apache configuration
├── utils/
│   └── jwt.php           # JWT utility functions
├── documents/            # Document storage (auto-created)
├── temp/                 # Temporary files (auto-created)
└── logs/                 # Log files (auto-created)
```

## Security Features

- JWT authentication for all OnlyOffice communications
- Document access validation
- Secure file serving with proper headers
- CORS configuration for OnlyOffice integration
- Protected directories via .htaccess

## Troubleshooting

### Common Issues

1. **OnlyOffice server not accessible**:
   - Check if Docker container is running
   - Verify port mappings (8080:80)
   - Test direct access to http://localhost:8080

2. **Callback not working**:
   - Check if `host.docker.internal` resolves from container
   - Try using actual IP address in `CALLBACK_URL_BASE`
   - Check firewall settings

3. **Documents not saving**:
   - Check write permissions on `documents/` directory
   - Review logs in `logs/onlyoffice.log`
   - Verify callback URL is reachable from OnlyOffice container

4. **JWT errors**:
   - Ensure JWT secret matches OnlyOffice configuration
   - Check system time synchronization
   - Review JWT token generation in logs

### Debug Mode

Enable debug mode in `config.php`:
```php
define('DEBUG_MODE', true);
```

This will show additional information and log more details.

### Log Files

Check the log file for detailed information:
```
logs/onlyoffice.log
```

## API Endpoints

- `GET /` - Main application page
- `GET /editor.php?key={documentKey}` - Document editor
- `GET /document.php?key={documentKey}` - Document serving
- `POST /callback.php?key={documentKey}` - OnlyOffice callbacks
- `GET /test-connection.php` - Connection testing

## Supported Document Types

- **Word Documents** (.docx) - Text editing
- **Excel Spreadsheets** (.xlsx) - Spreadsheet editing  
- **PowerPoint Presentations** (.pptx) - Presentation editing
- **Text Files** (.txt) - Simple text editing

## Next Steps for Production

1. **User Authentication**: Add proper user login and session management
2. **Database Integration**: Store document metadata in database
3. **File Versioning**: Implement document version history
4. **Permissions**: Add document sharing and permission controls
5. **UI/UX**: Improve interface design and user experience
6. **Error Handling**: Enhanced error reporting and recovery
7. **Performance**: Optimize for multiple concurrent users
8. **Security**: Additional security hardening and validation

## License

This is an MVP implementation for testing and development purposes.
