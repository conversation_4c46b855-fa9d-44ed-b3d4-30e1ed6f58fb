<?php
/**
 * OnlyOffice Document Server Integration Configuration
 * 
 * This file contains all configuration settings for the OnlyOffice integration
 */

// OnlyOffice Document Server Configuration
define('ONLYOFFICE_SERVER_URL', 'http://localhost:8080');
define('ONLYOFFICE_SERVER_URL_INTERNAL', 'http://localhost:8080'); // URL accessible from within Docker

// JWT Configuration - Using provided secrets
define('JWT_SECRET', 'mdMgBxplMBtliplZHzNRdVL6zKJLpdo2');
define('JWT_HEADER', 'Authorization');

// Application Configuration
define('APP_URL', 'http://localhost:8000'); // Base URL of this application
define('CALLBACK_URL_BASE', 'http://host.docker.internal:8000'); // URL accessible from Docker container

// Document Storage Configuration
define('DOCUMENTS_DIR', __DIR__ . '/documents');
define('TEMP_DIR', __DIR__ . '/temp');

// Supported Document Types
$DOCUMENT_TYPES = [
    'docx' => [
        'mime' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'type' => 'text',
        'name' => 'Word Document',
        'extension' => 'docx'
    ],
    'xlsx' => [
        'mime' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'type' => 'spreadsheet',
        'name' => 'Excel Spreadsheet',
        'extension' => 'xlsx'
    ],
    'pptx' => [
        'mime' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'type' => 'presentation',
        'name' => 'PowerPoint Presentation',
        'extension' => 'pptx'
    ],
    'txt' => [
        'mime' => 'text/plain',
        'type' => 'text',
        'name' => 'Text Document',
        'extension' => 'txt'
    ]
];

// OnlyOffice Editor Configuration
$EDITOR_CONFIG = [
    'mode' => 'edit',
    'lang' => 'en',
    'callbackUrl' => CALLBACK_URL_BASE . '/callback.php',
    'customization' => [
        'autosave' => true,
        'forcesave' => true,
        'commentAuthorOnly' => false,
        'comments' => true,
        'compactHeader' => false,
        'compactToolbar' => false,
        'compatibleFeatures' => false,
        'customer' => [
            'address' => 'OnlyOffice Integration MVP',
            'info' => 'Document Server Integration',
            'logo' => '',
            'mail' => '',
            'name' => 'MVP Application',
            'www' => ''
        ],
        'feedback' => [
            'url' => '',
            'visible' => false
        ],
        'goback' => [
            'url' => APP_URL,
            'text' => 'Back to Documents'
        ],
        'logo' => [
            'image' => '',
            'imageEmbedded' => '',
            'url' => APP_URL
        ],
        'mentionShare' => true,
        'review' => [
            'hideReviewDisplay' => false,
            'showReviewChanges' => false,
            'reviewDisplay' => 'original',
            'trackChanges' => false,
            'hoverMode' => false
        ],
        'showReviewChanges' => false,
        'spellcheck' => true,
        'toolbarNoTabs' => false,
        'unit' => 'cm',
        'zoom' => 100
    ]
];

// Error Logging Configuration
define('LOG_FILE', __DIR__ . '/logs/onlyoffice.log');
define('DEBUG_MODE', true);

// Helper Functions
function getDocumentTypes() {
    global $DOCUMENT_TYPES;
    return $DOCUMENT_TYPES;
}

function getEditorConfig() {
    global $EDITOR_CONFIG;
    return $EDITOR_CONFIG;
}

function logMessage($message, $level = 'INFO') {
    if (!DEBUG_MODE && $level === 'DEBUG') {
        return;
    }
    
    $logDir = dirname(LOG_FILE);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// Initialize directories
function initializeDirectories() {
    $dirs = [DOCUMENTS_DIR, TEMP_DIR, dirname(LOG_FILE)];
    
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                logMessage("Failed to create directory: $dir", 'ERROR');
                return false;
            }
            logMessage("Created directory: $dir", 'INFO');
        }
    }
    return true;
}

// Initialize on include
initializeDirectories();
logMessage("Configuration loaded successfully", 'INFO');
?>
