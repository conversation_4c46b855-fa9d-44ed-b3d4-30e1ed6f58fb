<?php
/**
 * OnlyOffice Document Server Integration - Document Serving Endpoint
 * 
 * This endpoint serves documents to the OnlyOffice server
 */

require_once 'config.php';
require_once 'utils/jwt.php';

// Get document key from URL
$documentKey = $_GET['key'] ?? '';

if (empty($documentKey)) {
    http_response_code(400);
    die('Document key is required');
}

// Load document metadata
$metadataFile = DOCUMENTS_DIR . '/' . $documentKey . '.meta';
if (!file_exists($metadataFile)) {
    http_response_code(404);
    die('Document not found');
}

$metadata = json_decode(file_get_contents($metadataFile), true);
if (!$metadata) {
    http_response_code(500);
    die('Invalid document metadata');
}

// Check if document file exists
$documentPath = DOCUMENTS_DIR . '/' . $metadata['filename'];
if (!file_exists($documentPath)) {
    http_response_code(404);
    die('Document file not found');
}

// Get document type configuration
$documentTypes = getDocumentTypes();
$typeConfig = $documentTypes[$metadata['type']];

// Validate JWT token if present
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
if (!empty($authHeader)) {
    $payload = validateCallbackRequest($authHeader);
    if (!$payload) {
        logMessage("Invalid JWT token for document access: $documentKey", 'ERROR');
        http_response_code(401);
        die('Unauthorized');
    }
    logMessage("Authorized document access: $documentKey", 'DEBUG');
}

// Set appropriate headers
header('Content-Type: ' . $typeConfig['mime']);
header('Content-Length: ' . filesize($documentPath));
header('Content-Disposition: inline; filename="' . $metadata['filename'] . '"');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Add CORS headers for OnlyOffice access
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// Handle OPTIONS request for CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

logMessage("Serving document: {$metadata['filename']} (key: $documentKey)", 'INFO');

// Serve the document
readfile($documentPath);
?>
