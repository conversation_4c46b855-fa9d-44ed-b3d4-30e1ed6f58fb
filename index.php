<?php
/**
 * OnlyOffice Document Server Integration - Main Page
 * 
 * This is the main page for creating and managing documents
 */

require_once 'config.php';
require_once 'utils/jwt.php';

// Handle document creation
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'create') {
    $documentType = $_POST['document_type'] ?? '';
    $documentName = trim($_POST['document_name'] ?? '');
    
    if (empty($documentName)) {
        $error = "Document name is required";
    } elseif (!isset(getDocumentTypes()[$documentType])) {
        $error = "Invalid document type";
    } else {
        // Create new document
        $result = createNewDocument($documentName, $documentType);
        if ($result['success']) {
            header('Location: editor.php?key=' . urlencode($result['key']));
            exit;
        } else {
            $error = $result['error'];
        }
    }
}

// Get list of existing documents
$documents = getExistingDocuments();

/**
 * Create a new document
 */
function createNewDocument($name, $type) {
    $documentTypes = getDocumentTypes();
    $typeConfig = $documentTypes[$type];
    
    // Generate unique filename
    $filename = sanitizeFilename($name) . '.' . $typeConfig['extension'];
    $filepath = DOCUMENTS_DIR . '/' . $filename;
    
    // Check if file already exists
    if (file_exists($filepath)) {
        return ['success' => false, 'error' => 'Document with this name already exists'];
    }
    
    // Create empty document based on type
    $success = createEmptyDocument($filepath, $type);
    
    if (!$success) {
        return ['success' => false, 'error' => 'Failed to create document'];
    }
    
    // Generate document key
    $documentKey = generateDocumentKey($filename);
    
    // Store document metadata
    $metadata = [
        'filename' => $filename,
        'original_name' => $name,
        'type' => $type,
        'key' => $documentKey,
        'created' => time(),
        'modified' => time()
    ];
    
    file_put_contents(DOCUMENTS_DIR . '/' . $documentKey . '.meta', json_encode($metadata));
    
    logMessage("Created new document: $filename (key: $documentKey)", 'INFO');
    
    return ['success' => true, 'key' => $documentKey, 'filename' => $filename];
}

/**
 * Create an empty document file
 */
function createEmptyDocument($filepath, $type) {
    switch ($type) {
        case 'txt':
            return file_put_contents($filepath, '') !== false;
        
        case 'docx':
            // Create minimal DOCX structure
            return createEmptyDocx($filepath);
        
        case 'xlsx':
            // Create minimal XLSX structure
            return createEmptyXlsx($filepath);
        
        case 'pptx':
            // Create minimal PPTX structure
            return createEmptyPptx($filepath);
        
        default:
            return false;
    }
}

/**
 * Create empty DOCX file
 */
function createEmptyDocx($filepath) {
    // For MVP, we'll create a simple text file and let OnlyOffice handle the conversion
    // In production, you'd want to create a proper DOCX structure
    $content = "<?xml version='1.0' encoding='UTF-8' standalone='yes'?>" .
               "<w:document xmlns:w='http://schemas.openxmlformats.org/wordprocessingml/2006/main'>" .
               "<w:body><w:p><w:r><w:t></w:t></w:r></w:p></w:body></w:document>";
    
    // For now, create a simple placeholder that OnlyOffice can work with
    return file_put_contents($filepath, '') !== false;
}

/**
 * Create empty XLSX file
 */
function createEmptyXlsx($filepath) {
    // Create a simple placeholder for OnlyOffice
    return file_put_contents($filepath, '') !== false;
}

/**
 * Create empty PPTX file
 */
function createEmptyPptx($filepath) {
    // Create a simple placeholder for OnlyOffice
    return file_put_contents($filepath, '') !== false;
}

/**
 * Sanitize filename
 */
function sanitizeFilename($filename) {
    // Remove or replace invalid characters
    $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $filename);
    $filename = preg_replace('/_{2,}/', '_', $filename);
    return trim($filename, '_');
}

/**
 * Get list of existing documents
 */
function getExistingDocuments() {
    $documents = [];
    $files = glob(DOCUMENTS_DIR . '/*.meta');
    
    foreach ($files as $metaFile) {
        $metadata = json_decode(file_get_contents($metaFile), true);
        if ($metadata) {
            $documents[] = $metadata;
        }
    }
    
    // Sort by creation date (newest first)
    usort($documents, function($a, $b) {
        return $b['created'] - $a['created'];
    });
    
    return $documents;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice Document Server - MVP</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .create-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .documents-list {
            margin-top: 30px;
        }
        .document-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .document-info {
            flex-grow: 1;
        }
        .document-name {
            font-weight: bold;
            color: #333;
        }
        .document-meta {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .document-actions {
            display: flex;
            gap: 10px;
        }
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OnlyOffice Document Server Integration - MVP</h1>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <div class="create-section">
            <h2>Create New Document</h2>
            <form method="POST">
                <input type="hidden" name="action" value="create">
                
                <div class="form-group">
                    <label for="document_name">Document Name:</label>
                    <input type="text" id="document_name" name="document_name" required 
                           placeholder="Enter document name (without extension)">
                </div>
                
                <div class="form-group">
                    <label for="document_type">Document Type:</label>
                    <select id="document_type" name="document_type" required>
                        <option value="">Select document type...</option>
                        <?php foreach (getDocumentTypes() as $key => $type): ?>
                            <option value="<?php echo $key; ?>"><?php echo htmlspecialchars($type['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <button type="submit">Create Document</button>
            </form>
        </div>
        
        <?php if (!empty($documents)): ?>
            <div class="documents-list">
                <h2>Existing Documents</h2>
                <?php foreach ($documents as $doc): ?>
                    <div class="document-item">
                        <div class="document-info">
                            <div class="document-name"><?php echo htmlspecialchars($doc['original_name']); ?></div>
                            <div class="document-meta">
                                Type: <?php echo htmlspecialchars(getDocumentTypes()[$doc['type']]['name']); ?> | 
                                Created: <?php echo date('Y-m-d H:i:s', $doc['created']); ?> |
                                Modified: <?php echo date('Y-m-d H:i:s', $doc['modified']); ?>
                            </div>
                        </div>
                        <div class="document-actions">
                            <a href="editor.php?key=<?php echo urlencode($doc['key']); ?>" 
                               class="btn-success btn-small" style="text-decoration: none; color: white;">
                                Open Editor
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="documents-list">
                <h2>No Documents Yet</h2>
                <p>Create your first document using the form above.</p>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
