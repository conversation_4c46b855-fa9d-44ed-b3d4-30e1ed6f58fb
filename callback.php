<?php
/**
 * OnlyOffice Document Server Integration - Callback Handler
 * 
 * This endpoint handles callbacks from OnlyOffice server for document saves
 */

require_once 'config.php';
require_once 'utils/jwt.php';

// Set JSON response header
header('Content-Type: application/json');

// Add CORS headers for OnlyOffice access
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Authorization, Content-Type');

// Handle OPTIONS request for CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Log the callback request
logMessage("Callback request received from OnlyOffice", 'INFO');
logMessage("Request method: " . $_SERVER['REQUEST_METHOD'], 'DEBUG');
logMessage("Request headers: " . json_encode(getallheaders()), 'DEBUG');

try {
    // Get document key from URL parameter
    $documentKey = $_GET['key'] ?? '';
    
    if (empty($documentKey)) {
        throw new Exception('Document key is required');
    }
    
    // Get request body
    $input = file_get_contents('php://input');
    logMessage("Callback request body: " . $input, 'DEBUG');
    
    $data = json_decode($input, true);
    if (!$data) {
        throw new Exception('Invalid JSON in request body');
    }
    
    // Validate JWT token if present
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (!empty($authHeader)) {
        $payload = validateCallbackRequest($authHeader);
        if (!$payload) {
            throw new Exception('Invalid JWT token');
        }
        logMessage("Callback JWT validated successfully", 'DEBUG');
    }
    
    // Load document metadata
    $metadataFile = DOCUMENTS_DIR . '/' . $documentKey . '.meta';
    if (!file_exists($metadataFile)) {
        throw new Exception('Document not found');
    }
    
    $metadata = json_decode(file_get_contents($metadataFile), true);
    if (!$metadata) {
        throw new Exception('Invalid document metadata');
    }
    
    // Process callback based on status
    $status = $data['status'] ?? 0;
    $url = $data['url'] ?? '';
    
    logMessage("Callback status: $status for document: $documentKey", 'INFO');
    
    $response = ['error' => 0];
    
    switch ($status) {
        case 1: // Document is being edited
            logMessage("Document is being edited: $documentKey", 'DEBUG');
            break;
            
        case 2: // Document is ready for saving
        case 3: // Document saving error has occurred
            if ($status === 2 && !empty($url)) {
                // Download and save the document
                $result = saveDocumentFromUrl($url, $documentKey, $metadata);
                if (!$result) {
                    $response['error'] = 1;
                    logMessage("Failed to save document from URL: $url", 'ERROR');
                } else {
                    logMessage("Document saved successfully: $documentKey", 'INFO');
                    
                    // Update metadata with modification time
                    $metadata['modified'] = time();
                    file_put_contents($metadataFile, json_encode($metadata));
                }
            } else if ($status === 3) {
                logMessage("Document saving error occurred: $documentKey", 'ERROR');
                $response['error'] = 1;
            }
            break;
            
        case 4: // Document is closed with no changes
            logMessage("Document closed with no changes: $documentKey", 'DEBUG');
            break;
            
        case 6: // Document is being edited, but the current document state is saved
        case 7: // Error has occurred while force saving the document
            if ($status === 6 && !empty($url)) {
                $result = saveDocumentFromUrl($url, $documentKey, $metadata);
                if ($result) {
                    logMessage("Document force saved successfully: $documentKey", 'INFO');
                    $metadata['modified'] = time();
                    file_put_contents($metadataFile, json_encode($metadata));
                }
            }
            break;
            
        default:
            logMessage("Unknown callback status: $status", 'WARNING');
            break;
    }
    
    // Send response
    echo json_encode($response);
    
} catch (Exception $e) {
    logMessage("Callback error: " . $e->getMessage(), 'ERROR');
    
    $response = ['error' => 1, 'message' => $e->getMessage()];
    echo json_encode($response);
}

/**
 * Save document from OnlyOffice URL
 */
function saveDocumentFromUrl($url, $documentKey, $metadata) {
    try {
        // Create context for file_get_contents with timeout
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'method' => 'GET',
                'header' => [
                    'User-Agent: OnlyOffice-Callback-Client/1.0'
                ]
            ]
        ]);
        
        // Download the document content
        $content = file_get_contents($url, false, $context);
        
        if ($content === false) {
            logMessage("Failed to download document from URL: $url", 'ERROR');
            return false;
        }
        
        // Save to document file
        $documentPath = DOCUMENTS_DIR . '/' . $metadata['filename'];
        $result = file_put_contents($documentPath, $content);
        
        if ($result === false) {
            logMessage("Failed to write document to file: $documentPath", 'ERROR');
            return false;
        }
        
        logMessage("Document saved: {$metadata['filename']} (" . strlen($content) . " bytes)", 'INFO');
        return true;
        
    } catch (Exception $e) {
        logMessage("Error saving document: " . $e->getMessage(), 'ERROR');
        return false;
    }
}
?>
