<?php
/**
 * OnlyOffice Document Server Integration - Editor Page
 * 
 * This page loads the OnlyOffice editor for document editing
 */

require_once 'config.php';
require_once 'utils/jwt.php';

// Get document key from URL
$documentKey = $_GET['key'] ?? '';

if (empty($documentKey)) {
    die('Document key is required');
}

// Load document metadata
$metadataFile = DOCUMENTS_DIR . '/' . $documentKey . '.meta';
if (!file_exists($metadataFile)) {
    die('Document not found');
}

$metadata = json_decode(file_get_contents($metadataFile), true);
if (!$metadata) {
    die('Invalid document metadata');
}

// Check if document file exists
$documentPath = DOCUMENTS_DIR . '/' . $metadata['filename'];
if (!file_exists($documentPath)) {
    die('Document file not found');
}

// Get document type configuration
$documentTypes = getDocumentTypes();
$typeConfig = $documentTypes[$metadata['type']];

// Generate document URL (accessible by OnlyOffice server)
$documentUrl = APP_URL . '/document.php?key=' . urlencode($documentKey);

// Generate callback URL (accessible by OnlyOffice server)
$callbackUrl = getCallbackUrl($documentKey);

// Prepare document configuration for OnlyOffice
$documentConfig = [
    'document' => [
        'fileType' => $typeConfig['extension'],
        'key' => $documentKey,
        'title' => $metadata['original_name'] . '.' . $typeConfig['extension'],
        'url' => $documentUrl,
        'permissions' => [
            'comment' => true,
            'copy' => true,
            'download' => true,
            'edit' => true,
            'fillForms' => true,
            'modifyFilter' => true,
            'modifyContentControl' => true,
            'review' => true,
            'print' => true
        ]
    ],
    'documentType' => $typeConfig['type'],
    'editorConfig' => [
        'mode' => 'edit',
        'lang' => 'en',
        'callbackUrl' => $callbackUrl,
        'user' => [
            'id' => 'user_' . session_id(),
            'name' => 'Anonymous User'
        ],
        'customization' => getEditorConfig()['customization']
    ],
    'width' => '100%',
    'height' => '600px'
];

// Generate JWT token for the configuration
$jwtToken = generateDocumentJWT($documentConfig);

logMessage("Loading editor for document: {$metadata['filename']} (key: $documentKey)", 'INFO');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit: <?php echo htmlspecialchars($metadata['original_name']); ?></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: #fff;
            padding: 10px 20px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .header .actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            border: none;
            cursor: pointer;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .editor-container {
            width: 100%;
            height: calc(100vh - 60px);
            background: white;
        }
        #onlyoffice-editor {
            width: 100%;
            height: 100%;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px;
            font-size: 18px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            margin: 20px;
            border-radius: 4px;
            text-align: center;
        }
        .debug-info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            margin: 10px 20px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Editing: <?php echo htmlspecialchars($metadata['original_name']); ?></h1>
        <div class="actions">
            <a href="index.php" class="btn btn-secondary">Back to Documents</a>
            <button onclick="saveDocument()" class="btn btn-primary">Save</button>
        </div>
    </div>
    
    <?php if (DEBUG_MODE): ?>
    <div class="debug-info">
        <strong>Debug Info:</strong><br>
        Document Key: <?php echo htmlspecialchars($documentKey); ?><br>
        Document URL: <?php echo htmlspecialchars($documentUrl); ?><br>
        Callback URL: <?php echo htmlspecialchars($callbackUrl); ?><br>
        OnlyOffice Server: <?php echo htmlspecialchars(ONLYOFFICE_SERVER_URL); ?><br>
        JWT Enabled: Yes
    </div>
    <?php endif; ?>
    
    <div class="editor-container">
        <div id="onlyoffice-editor">
            <div class="loading">Loading OnlyOffice Editor...</div>
        </div>
    </div>

    <script type="text/javascript" src="<?php echo ONLYOFFICE_SERVER_URL; ?>/web-apps/apps/api/documents/api.js"></script>
    <script type="text/javascript">
        let docEditor;
        
        window.onload = function() {
            try {
                // OnlyOffice Document Editor configuration
                const config = <?php echo json_encode($documentConfig); ?>;
                
                // Add JWT token to configuration
                config.token = "<?php echo $jwtToken; ?>";
                
                // Add event handlers
                config.events = {
                    'onDocumentReady': onDocumentReady,
                    'onError': onError,
                    'onWarning': onWarning,
                    'onInfo': onInfo
                };
                
                console.log('Initializing OnlyOffice Editor with config:', config);
                
                // Initialize the editor
                docEditor = new DocsAPI.DocEditor("onlyoffice-editor", config);
                
            } catch (error) {
                console.error('Error initializing OnlyOffice Editor:', error);
                showError('Failed to initialize editor: ' + error.message);
            }
        };
        
        function onDocumentReady() {
            console.log('Document is ready for editing');
        }
        
        function onError(event) {
            console.error('OnlyOffice Editor Error:', event);
            showError('Editor Error: ' + (event.data || 'Unknown error'));
        }
        
        function onWarning(event) {
            console.warn('OnlyOffice Editor Warning:', event);
        }
        
        function onInfo(event) {
            console.info('OnlyOffice Editor Info:', event);
        }
        
        function saveDocument() {
            if (docEditor) {
                docEditor.downloadAs();
            }
        }
        
        function showError(message) {
            const editorDiv = document.getElementById('onlyoffice-editor');
            editorDiv.innerHTML = '<div class="error">' + message + '</div>';
        }
        
        // Handle page unload
        window.addEventListener('beforeunload', function(e) {
            // OnlyOffice will handle auto-save through callbacks
        });
    </script>
</body>
</html>
