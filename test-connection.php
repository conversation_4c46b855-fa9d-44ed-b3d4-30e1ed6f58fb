<?php
/**
 * OnlyOffice Connection Test
 * 
 * This script tests the connection to OnlyOffice Document Server
 */

require_once 'config.php';

echo "<h1>OnlyOffice Document Server Connection Test</h1>";

// Test 1: Check if OnlyOffice server is accessible
echo "<h2>Test 1: Server Accessibility</h2>";
$onlyofficeUrl = ONLYOFFICE_SERVER_URL;
echo "Testing connection to: $onlyofficeUrl<br>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$result = @file_get_contents($onlyofficeUrl, false, $context);
if ($result !== false) {
    echo "✅ OnlyOffice server is accessible<br>";
} else {
    echo "❌ OnlyOffice server is not accessible<br>";
    echo "Error: " . error_get_last()['message'] . "<br>";
}

// Test 2: Check API endpoint
echo "<h2>Test 2: API Endpoint</h2>";
$apiUrl = $onlyofficeUrl . '/web-apps/apps/api/documents/api.js';
echo "Testing API endpoint: $apiUrl<br>";

$apiResult = @file_get_contents($apiUrl, false, $context);
if ($apiResult !== false && strpos($apiResult, 'DocsAPI') !== false) {
    echo "✅ OnlyOffice API is accessible<br>";
} else {
    echo "❌ OnlyOffice API is not accessible<br>";
}

// Test 3: Check directories
echo "<h2>Test 3: Directory Structure</h2>";
$dirs = [
    'Documents' => DOCUMENTS_DIR,
    'Temp' => TEMP_DIR,
    'Logs' => dirname(LOG_FILE)
];

foreach ($dirs as $name => $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        echo "✅ $name directory exists and is writable: $dir<br>";
    } else {
        echo "❌ $name directory issue: $dir<br>";
        if (!is_dir($dir)) {
            echo "&nbsp;&nbsp;Directory does not exist<br>";
        } elseif (!is_writable($dir)) {
            echo "&nbsp;&nbsp;Directory is not writable<br>";
        }
    }
}

// Test 4: JWT functionality
echo "<h2>Test 4: JWT Functionality</h2>";
require_once 'utils/jwt.php';

try {
    $testPayload = ['test' => 'data', 'timestamp' => time()];
    $token = generateJWT($testPayload);
    echo "✅ JWT generation successful<br>";
    
    $decoded = validateJWT($token);
    if ($decoded && $decoded['test'] === 'data') {
        echo "✅ JWT validation successful<br>";
    } else {
        echo "❌ JWT validation failed<br>";
    }
} catch (Exception $e) {
    echo "❌ JWT error: " . $e->getMessage() . "<br>";
}

// Test 5: Configuration
echo "<h2>Test 5: Configuration</h2>";
echo "OnlyOffice Server URL: " . ONLYOFFICE_SERVER_URL . "<br>";
echo "App URL: " . APP_URL . "<br>";
echo "Callback URL Base: " . CALLBACK_URL_BASE . "<br>";
echo "JWT Secret: " . (JWT_SECRET ? "✅ Configured" : "❌ Not configured") . "<br>";

// Test 6: Document types
echo "<h2>Test 6: Document Types</h2>";
$documentTypes = getDocumentTypes();
foreach ($documentTypes as $key => $type) {
    echo "✅ {$type['name']} (.{$type['extension']}) - {$type['type']}<br>";
}

echo "<h2>Summary</h2>";
echo "If all tests pass, you can proceed to create documents using the main application.<br>";
echo "<a href='index.php'>Go to Main Application</a>";
?>
